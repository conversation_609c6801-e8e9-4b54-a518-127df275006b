local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP", "vrp_grape_farmer")

-- متغيرات لتتبع اللاعبين
local grapeFarmers = {}
local playerCooldowns = {}

-- دالة للتحقق من الكولداون
local function checkPlayerCooldown(user_id, actionType)
    local currentTime = os.time()
    local cooldownTime = Config.Cooldowns[actionType]

    if not playerCooldowns[user_id] then
        playerCooldowns[user_id] = {}
    end

    if playerCooldowns[user_id][actionType] and currentTime - playerCooldowns[user_id][actionType] < cooldownTime then
        return false
    end

    playerCooldowns[user_id][actionType] = currentTime
    return true
end

-- دالة لبدء الوظيفة
RegisterNetEvent('vrp_grape_farmer:startJob')
AddEventHandler('vrp_grape_farmer:startJob', function()
    local user_id = vRP.getUserId({source})
    if not user_id then return end

    -- التحقق من وجود سلة الجمع
    if not vRP.hasInventoryItem({user_id, Config.Items.basket, 1}) then
        vRPclient.notify(source, {Config.Locale['need_basket']})
        return
    end

    -- تعيين الوظيفة
    vRP.addUserGroup({user_id, Config.Job.name})
    grapeFarmers[user_id] = true

    vRPclient.notify(source, {Config.Locale['job_started']})
    TriggerClientEvent('vrp_grape_farmer:jobStarted', source)
end)

-- دالة لإنهاء الوظيفة
RegisterNetEvent('vrp_grape_farmer:stopJob')
AddEventHandler('vrp_grape_farmer:stopJob', function()
    local user_id = vRP.getUserId({source})
    if not user_id then return end

    -- إزالة الوظيفة
    vRP.removeUserGroup({user_id, Config.Job.name})
    grapeFarmers[user_id] = nil

    vRPclient.notify(source, {Config.Locale['job_stopped']})
    TriggerClientEvent('vrp_grape_farmer:jobStopped', source)
end)

-- دالة لجمع العنب
RegisterNetEvent('vrp_grape_farmer:collectGrapes')
AddEventHandler('vrp_grape_farmer:collectGrapes', function()
    local user_id = vRP.getUserId({source})
    if not user_id then return end

    -- التحقق من الوظيفة
    if not grapeFarmers[user_id] then
        vRPclient.notify(source, {Config.Locale['not_grape_farmer']})
        return
    end

    -- التحقق من الكولداون
    if not checkPlayerCooldown(user_id, 'collect') then
        vRPclient.notify(source, {Config.Locale['cooldown']})
        return
    end

    -- التحقق من وجود سلة الجمع
    if not vRP.hasInventoryItem({user_id, Config.Items.basket, 1}) then
        vRPclient.notify(source, {Config.Locale['need_basket']})
        return
    end

    -- التحقق من مساحة الحقيبة
    if not vRP.tryGetInventoryItem({user_id, Config.Items.grapes, Config.Prices.grapes_per_collect, true}) then
        vRPclient.notify(source, {Config.Locale['inventory_full']})
        return
    end

    -- إعطاء العنب
    vRP.giveInventoryItem({user_id, Config.Items.grapes, Config.Prices.grapes_per_collect, true})
    vRPclient.notify(source, {string.format(Config.Locale['collected_grapes'], Config.Prices.grapes_per_collect)})
end)

-- دالة لتحويل العنب إلى عصير
RegisterNetEvent('vrp_grape_farmer:processGrapes')
AddEventHandler('vrp_grape_farmer:processGrapes', function()
    local user_id = vRP.getUserId({source})
    if not user_id then return end

    -- التحقق من الوظيفة
    if not grapeFarmers[user_id] then
        vRPclient.notify(source, {Config.Locale['not_grape_farmer']})
        return
    end

    -- التحقق من الكولداون
    if not checkPlayerCooldown(user_id, 'process') then
        vRPclient.notify(source, {Config.Locale['cooldown']})
        return
    end

    -- التحقق من وجود العنب
    if not vRP.hasInventoryItem({user_id, Config.Items.grapes, 5}) then
        vRPclient.notify(source, {Config.Locale['no_grapes']})
        return
    end

    -- التحقق من مساحة الحقيبة للعصير
    if not vRP.tryGetInventoryItem({user_id, Config.Items.grape_juice, Config.Prices.juice_per_process, true}) then
        vRPclient.notify(source, {Config.Locale['inventory_full']})
        return
    end

    -- أخذ العنب وإعطاء العصير
    vRP.tryGetInventoryItem({user_id, Config.Items.grapes, 5, true})
    vRP.giveInventoryItem({user_id, Config.Items.grape_juice, Config.Prices.juice_per_process, true})
    vRPclient.notify(source, {string.format(Config.Locale['processed_juice'], Config.Prices.juice_per_process)})
end)

-- دالة لبيع عصير العنب
RegisterNetEvent('vrp_grape_farmer:sellJuice')
AddEventHandler('vrp_grape_farmer:sellJuice', function()
    local user_id = vRP.getUserId({source})
    if not user_id then return end

    -- التحقق من الوظيفة
    if not grapeFarmers[user_id] then
        vRPclient.notify(source, {Config.Locale['not_grape_farmer']})
        return
    end

    -- التحقق من الكولداون
    if not checkPlayerCooldown(user_id, 'sell') then
        vRPclient.notify(source, {Config.Locale['cooldown']})
        return
    end

    -- التحقق من وجود عصير العنب
    local juiceAmount = vRP.getInventoryItemAmount({user_id, Config.Items.grape_juice})
    if juiceAmount <= 0 then
        vRPclient.notify(source, {Config.Locale['no_juice']})
        return
    end

    -- حساب المبلغ الإجمالي
    local totalPrice = juiceAmount * Config.Prices.grape_juice_price

    -- أخذ العصير وإعطاء المال
    vRP.tryGetInventoryItem({user_id, Config.Items.grape_juice, juiceAmount, true})
    vRP.giveMoney({user_id, totalPrice})
    vRPclient.notify(source, {string.format(Config.Locale['sold_juice'], totalPrice)})
end)

-- تنظيف البيانات عند قطع الاتصال
AddEventHandler('vRP:playerLeave', function(user_id, source)
    grapeFarmers[user_id] = nil
    playerCooldowns[user_id] = nil
end)
