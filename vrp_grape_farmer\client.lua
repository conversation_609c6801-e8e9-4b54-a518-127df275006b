local Tunnel = module("vrp", "lib/Tunnel")
local Proxy = module("vrp", "lib/Proxy")

vRP = Proxy.getInterface("vRP")
vRPclient = Tunnel.getInterface("vRP", "vrp_grape_farmer")

-- متغيرات محلية
local isGrapeFarmer = false
local lastAction = 0
local blips = {}

-- دالة للتحقق من الكولداون
local function checkCooldown(actionType)
    local currentTime = GetGameTimer()
    local cooldownTime = Config.Cooldowns[actionType] * 1000
    
    if currentTime - lastAction < cooldownTime then
        vRP.notify({Config.Locale['cooldown']})
        return false
    end
    
    lastAction = currentTime
    return true
end

-- دالة لإنشاء البليبس
local function createBlips()
    -- بليب نقطة البداية
    local startBlip = AddBlipForCoord(Config.JobStart.coords.x, Config.JobStart.coords.y, Config.JobStart.coords.z)
    SetBlipSprite(startBlip, Config.JobStart.blip.sprite)
    SetBlipColour(startBlip, Config.JobStart.blip.color)
    SetBlipScale(startBlip, Config.JobStart.blip.scale)
    SetBlipAsShortRange(startBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.JobStart.blip.label)
    EndTextCommandSetBlipName(startBlip)
    table.insert(blips, startBlip)
end

-- دالة لإنشاء بليبس الوظيفة
local function createJobBlips()
    -- بليبس جمع العنب
    for i, location in ipairs(Config.GrapeLocations) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.blip.sprite)
        SetBlipColour(blip, location.blip.color)
        SetBlipScale(blip, location.blip.scale)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.blip.label)
        EndTextCommandSetBlipName(blip)
        table.insert(blips, blip)
    end
    
    -- بليب التحويل
    local processBlip = AddBlipForCoord(Config.ProcessLocation.coords.x, Config.ProcessLocation.coords.y, Config.ProcessLocation.coords.z)
    SetBlipSprite(processBlip, Config.ProcessLocation.blip.sprite)
    SetBlipColour(processBlip, Config.ProcessLocation.blip.color)
    SetBlipScale(processBlip, Config.ProcessLocation.blip.scale)
    SetBlipAsShortRange(processBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.ProcessLocation.blip.label)
    EndTextCommandSetBlipName(processBlip)
    table.insert(blips, processBlip)
    
    -- بليب البيع
    local sellBlip = AddBlipForCoord(Config.SellLocation.coords.x, Config.SellLocation.coords.y, Config.SellLocation.coords.z)
    SetBlipSprite(sellBlip, Config.SellLocation.blip.sprite)
    SetBlipColour(sellBlip, Config.SellLocation.blip.color)
    SetBlipScale(sellBlip, Config.SellLocation.blip.scale)
    SetBlipAsShortRange(sellBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.SellLocation.blip.label)
    EndTextCommandSetBlipName(sellBlip)
    table.insert(blips, sellBlip)
end

-- دالة لحذف بليبس الوظيفة
local function removeJobBlips()
    for i = 2, #blips do -- نبدأ من 2 للحفاظ على بليب نقطة البداية
        RemoveBlip(blips[i])
    end
    -- نحتفظ فقط ببليب نقطة البداية
    local startBlip = blips[1]
    blips = {startBlip}
end

-- دالة لبدء الوظيفة
local function startJob()
    TriggerServerEvent('vrp_grape_farmer:startJob')
end

-- دالة لإنهاء الوظيفة
local function stopJob()
    TriggerServerEvent('vrp_grape_farmer:stopJob')
end

-- دالة لجمع العنب
local function collectGrapes()
    if not checkCooldown('collect') then return end
    
    local playerPed = PlayerPedId()
    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_GARDENER_PLANT", 0, true)
    
    Citizen.Wait(3000)
    
    ClearPedTasks(playerPed)
    TriggerServerEvent('vrp_grape_farmer:collectGrapes')
end

-- دالة لتحويل العنب
local function processGrapes()
    if not checkCooldown('process') then return end
    
    local playerPed = PlayerPedId()
    TaskStartScenarioInPlace(playerPed, "PROP_HUMAN_PARKING_METER", 0, true)
    
    Citizen.Wait(5000)
    
    ClearPedTasks(playerPed)
    TriggerServerEvent('vrp_grape_farmer:processGrapes')
end

-- دالة لبيع العصير
local function sellJuice()
    if not checkCooldown('sell') then return end
    
    TriggerServerEvent('vrp_grape_farmer:sellJuice')
end

-- الأحداث من الخادم
RegisterNetEvent('vrp_grape_farmer:jobStarted')
AddEventHandler('vrp_grape_farmer:jobStarted', function()
    isGrapeFarmer = true
    createJobBlips()
end)

RegisterNetEvent('vrp_grape_farmer:jobStopped')
AddEventHandler('vrp_grape_farmer:jobStopped', function()
    isGrapeFarmer = false
    removeJobBlips()
end)

-- الحلقة الرئيسية
Citizen.CreateThread(function()
    createBlips()
    
    while true do
        Citizen.Wait(0)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        
        -- نقطة بدء الوظيفة
        local startDistance = #(playerCoords - Config.JobStart.coords)
        if startDistance < Config.Distances.draw_text then
            if not isGrapeFarmer then
                DrawText3D(Config.JobStart.coords.x, Config.JobStart.coords.y, Config.JobStart.coords.z, Config.Locale['start_job'])
                if startDistance < Config.Distances.interact and IsControlJustPressed(0, 38) then
                    startJob()
                end
            else
                DrawText3D(Config.JobStart.coords.x, Config.JobStart.coords.y, Config.JobStart.coords.z, Config.Locale['stop_job'])
                if startDistance < Config.Distances.interact and IsControlJustPressed(0, 38) then
                    stopJob()
                end
            end
        end
        
        if isGrapeFarmer then
            -- نقاط جمع العنب
            for i, location in ipairs(Config.GrapeLocations) do
                local distance = #(playerCoords - location.coords)
                if distance < Config.Distances.draw_text then
                    DrawText3D(location.coords.x, location.coords.y, location.coords.z, Config.Locale['collect_grapes'])
                    if distance < Config.Distances.interact and IsControlJustPressed(0, 38) then
                        collectGrapes()
                    end
                end
            end
            
            -- نقطة التحويل
            local processDistance = #(playerCoords - Config.ProcessLocation.coords)
            if processDistance < Config.Distances.draw_text then
                DrawText3D(Config.ProcessLocation.coords.x, Config.ProcessLocation.coords.y, Config.ProcessLocation.coords.z, Config.Locale['process_grapes'])
                if processDistance < Config.Distances.interact and IsControlJustPressed(0, 38) then
                    processGrapes()
                end
            end
            
            -- نقطة البيع
            local sellDistance = #(playerCoords - Config.SellLocation.coords)
            if sellDistance < Config.Distances.draw_text then
                DrawText3D(Config.SellLocation.coords.x, Config.SellLocation.coords.y, Config.SellLocation.coords.z, Config.Locale['sell_juice'])
                if sellDistance < Config.Distances.interact and IsControlJustPressed(0, 38) then
                    sellJuice()
                end
            end
        end
    end
end)

-- دالة لرسم النص ثلاثي الأبعاد
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end
