# سكربت وظيفة مزارع العنب - vRP Grape Farmer

## الوصف
سكربت وظيفة مزارع العنب لخادم FiveM باستخدام إطار عمل vRP. يتيح للاعبين العمل كمزارعي عنب وجمع العنب وتحويله إلى عصير وبيعه مقابل المال.

## المميزات
- ✅ نقطة بدء الوظيفة مع بليب على الخريطة
- ✅ 3 نقاط مختلفة لجمع العنب
- ✅ نقطة تحويل العنب إلى عصير
- ✅ نقطة بيع عصير العنب
- ✅ نظام cooldown لمنع الاستغلال
- ✅ التحقق من المعدات المطلوبة (سلة الجمع)
- ✅ رسائل مترجمة باللغة العربية
- ✅ أنيميشن للاعب أثناء العمل
- ✅ نظام بليبس متكامل

## التثبيت

### 1. نسخ الملفات
```bash
# انسخ مجلد vrp_grape_farmer إلى مجلد resources في خادمك
cp -r vrp_grape_farmer /path/to/your/server/resources/
```

### 2. إضافة الريسورس إلى server.cfg
```cfg
# أضف هذا السطر إلى ملف server.cfg
start vrp_grape_farmer
```

### 3. إضافة العناصر إلى قاعدة البيانات
أضف العناصر التالية إلى جدول `vrp_items` في قاعدة البيانات:

```sql
INSERT INTO `vrp_items` (`item`, `label`, `description`, `weight`) VALUES
('grape_basket', 'سلة جمع العنب', 'سلة لجمع العنب من المزرعة', 1.0),
('grapes', 'عنب', 'عنب طازج من المزرعة', 0.5),
('grape_juice', 'عصير عنب', 'عصير عنب طبيعي ولذيذ', 0.3);
```

### 4. إضافة الوظيفة (اختياري)
إذا كنت تريد إضافة الوظيفة إلى نظام الوظائف في vRP:

```sql
INSERT INTO `vrp_user_groups` (`user_id`, `group`, `subgroup`) VALUES
-- سيتم إضافة اللاعبين تلقائياً عند بدء العمل
```

## الاستخدام

### للاعبين:
1. **الحصول على سلة الجمع**: تحتاج إلى سلة جمع (`grape_basket`) لبدء العمل
2. **بدء العمل**: اذهب إلى نقطة بدء الوظيفة واضغط E
3. **جمع العنب**: اذهب إلى نقاط جمع العنب واضغط E
4. **تحويل العنب**: اذهب إلى نقطة التحويل لتحويل العنب إلى عصير
5. **بيع العصير**: اذهب إلى نقطة البيع لبيع عصير العنب

### للمطورين:
يمكنك تخصيص السكربت من خلال تعديل ملف `config.lua`:

- **المواقع**: غير إحداثيات النقاط حسب خريطتك
- **الأسعار**: عدل أسعار البيع والكميات
- **أوقات الانتظار**: غير أوقات الـ cooldown
- **الرسائل**: عدل النصوص والترجمات

## الإعدادات

### المواقع الافتراضية
- **نقطة البداية**: `2222.78, 5577.03, 53.85`
- **نقاط جمع العنب**: 3 نقاط مختلفة حول المزرعة
- **نقطة التحويل**: `2212.34, 5585.67, 53.85`
- **نقطة البيع**: `2205.89, 5580.12, 53.85`

### الأسعار الافتراضية
- **سعر بيع عصير العنب**: $50 لكل وحدة
- **كمية العنب في كل جمع**: 3 وحدات
- **كمية العصير من التحويل**: 2 وحدة

### أوقات الانتظار
- **جمع العنب**: 5 ثوان
- **تحويل العنب**: 8 ثوان
- **بيع العصير**: 3 ثوان

## الأوامر الإدارية
```lua
-- إعطاء سلة جمع للاعب
/giveitem [player_id] grape_basket 1

-- إعطاء عنب للاعب
/giveitem [player_id] grapes 10

-- إعطاء عصير عنب للاعب
/giveitem [player_id] grape_juice 5
```

## استكشاف الأخطاء

### مشاكل شائعة:
1. **"تحتاج إلى سلة جمع"**: تأكد من وجود العنصر `grape_basket` في الحقيبة
2. **"حقيبتك ممتلئة"**: تأكد من وجود مساحة كافية في الحقيبة
3. **البليبس لا تظهر**: تأكد من بدء الريسورس بشكل صحيح

### سجلات الأخطاء:
تحقق من console الخادم للأخطاء المتعلقة بـ `vrp_grape_farmer`

## الدعم
إذا واجهت أي مشاكل، تأكد من:
- تثبيت vRP بشكل صحيح
- إضافة العناصر إلى قاعدة البيانات
- بدء الريسورس في server.cfg

## الترخيص
هذا السكربت مجاني للاستخدام والتعديل.

---
**تم تطويره بواسطة Augment Agent**
