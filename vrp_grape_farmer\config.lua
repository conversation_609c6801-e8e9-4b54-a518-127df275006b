Config = {}

-- اللغة والرسائل
Config.Locale = {
    ['start_job'] = 'اضغط ~INPUT_CONTEXT~ لبدء العمل كمزارع عنب',
    ['stop_job'] = 'اضغط ~INPUT_CONTEXT~ لإنهاء العمل',
    ['collect_grapes'] = 'اضغط ~INPUT_CONTEXT~ لجمع العنب',
    ['process_grapes'] = 'اضغط ~INPUT_CONTEXT~ لتحويل العنب إلى عصير',
    ['sell_juice'] = 'اضغط ~INPUT_CONTEXT~ لبيع عصير العنب',
    ['job_started'] = 'بدأت العمل كمزارع عنب!',
    ['job_stopped'] = 'توقفت عن العمل كمزارع عنب',
    ['collected_grapes'] = 'جمعت عنب! (+%s)',
    ['processed_juice'] = 'حولت العنب إلى عصير! (+%s)',
    ['sold_juice'] = 'بعت عصير العنب مقابل $%s',
    ['need_basket'] = 'تحتاج إلى سلة جمع للعمل!',
    ['no_grapes'] = 'ليس لديك عنب للتحويل!',
    ['no_juice'] = 'ليس لديك عصير عنب للبيع!',
    ['cooldown'] = 'انتظر قليلاً قبل المحاولة مرة أخرى!',
    ['inventory_full'] = 'حقيبتك ممتلئة!',
    ['not_grape_farmer'] = 'لست مزارع عنب!'
}

-- إعدادات الوظيفة
Config.Job = {
    name = 'grape_farmer',
    label = 'مزارع العنب'
}

-- العناصر المطلوبة
Config.Items = {
    basket = 'grape_basket',      -- سلة الجمع
    grapes = 'grapes',           -- العنب
    grape_juice = 'grape_juice'   -- عصير العنب
}

-- الأسعار والكميات
Config.Prices = {
    grape_juice_price = 50,      -- سعر بيع عصير العنب
    grapes_per_collect = 3,      -- كمية العنب في كل جمع
    juice_per_process = 2        -- كمية العصير من كل تحويل
}

-- أوقات الانتظار (بالثواني)
Config.Cooldowns = {
    collect = 5,    -- انتظار بين جمع العنب
    process = 8,    -- انتظار تحويل العنب
    sell = 3        -- انتظار بيع العصير
}

-- نقطة بدء الوظيفة
Config.JobStart = {
    coords = vector3(2222.78, 5577.03, 53.85),
    blip = {
        sprite = 85,
        color = 25,
        scale = 0.8,
        label = 'مزرعة العنب'
    }
}

-- نقاط جمع العنب
Config.GrapeLocations = {
    {
        coords = vector3(2224.12, 5576.89, 53.85),
        blip = {
            sprite = 1,
            color = 25,
            scale = 0.6,
            label = 'جمع العنب'
        }
    },
    {
        coords = vector3(2230.45, 5582.12, 53.85),
        blip = {
            sprite = 1,
            color = 25,
            scale = 0.6,
            label = 'جمع العنب'
        }
    },
    {
        coords = vector3(2218.67, 5590.34, 53.85),
        blip = {
            sprite = 1,
            color = 25,
            scale = 0.6,
            label = 'جمع العنب'
        }
    }
}

-- نقطة تحويل العنب
Config.ProcessLocation = {
    coords = vector3(2212.34, 5585.67, 53.85),
    blip = {
        sprite = 478,
        color = 25,
        scale = 0.7,
        label = 'تحويل العنب'
    }
}

-- نقطة بيع عصير العنب
Config.SellLocation = {
    coords = vector3(2205.89, 5580.12, 53.85),
    blip = {
        sprite = 500,
        color = 2,
        scale = 0.8,
        label = 'بيع عصير العنب'
    }
}

-- المسافات
Config.Distances = {
    interact = 2.0,    -- مسافة التفاعل
    draw_text = 5.0    -- مسافة عرض النص
}
