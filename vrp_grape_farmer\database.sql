-- ملف SQL لإضافة عناصر وظيفة مزارع العنب إلى قاعدة البيانات

-- إضافة العناصر إلى جدول vrp_items
INSERT INTO `vrp_items` (`item`, `label`, `description`, `weight`) VALUES
('grape_basket', 'سلة جمع العنب', 'سلة خاصة لجمع العنب من المزرعة', 1.0),
('grapes', 'عنب', 'عنب طازج ولذيذ من المزرعة', 0.5),
('grape_juice', 'عصير عنب', 'عصير عنب طبيعي ومنعش', 0.3);

-- إضافة الوظيفة إلى جدول المجموعات (اختياري)
-- يمكنك إضافة هذا إذا كنت تريد تتبع الوظيفة في نظام المجموعات
-- INSERT INTO `vrp_groups` (`group`, `title`, `salary`) VALUES
-- ('grape_farmer', 'مزارع العنب', 0);

-- ملاحظات:
-- 1. تأكد من أن جدول vrp_items موجود في قاعدة البيانات
-- 2. قد تحتاج إلى تعديل أسماء الجداول حسب إعداد vRP الخاص بك
-- 3. الأوزان يمكن تعديلها حسب نظام الحقيبة في خادمك
